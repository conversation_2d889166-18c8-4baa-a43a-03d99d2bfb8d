import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  ShieldCheck, Menu, X, ChevronDown, User, LogOut, Settings, Shield, 
  Heart, Globe, Users, Target, Award, HandHeart, Handshake, Phone,
  AlertTriangle, Info, HelpCircle, MapPin, Zap, BookOpen, FileText,
  Languages, DollarSign, Sparkles
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../hooks/useAuth';
import { useRoles } from '../../hooks/useRoles';
import Avatar from '../Common/Avatar';
import LanguageSwitcher from '../LanguageSwitcher/LanguageSwitcher';

interface NavItem {
  name: string;
  path: string;
  dropdown?: DropdownItem[];
}

interface DropdownItem {
  name: string;
  path: string;
  icon?: React.ReactNode;
  description?: string;
}

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const location = useLocation();
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated, logout } = useAuth();
  const { isAdmin, isCj, hasAdminOrCjRole, isOnlyUser, formatRoleName } = useRoles();
  const { t } = useTranslation();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setIsUserDropdownOpen(false);
      }
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getNavItems = (): NavItem[] => {
    const baseItems: NavItem[] = [
      { name: t('navigation.home'), path: '/' },
      {
        name: t('navigation.about'),
        path: '/about',
        dropdown: [
          {
            name: 'Overview',
            path: '/about',
            icon: <Target size={16} />,
            description: 'Learn about our mission and vision'
          },
          {
            name: 'What We Do',
            path: '/what-we-do',
            icon: <HandHeart size={16} />,
            description: 'Our disaster response services'
          },
          {
            name: 'Leadership',
            path: '/leadership',
            icon: <Award size={16} />,
            description: 'Meet our leadership team'
          }
        ]
      },
      {
        name: 'Take Action',
        path: '/get-involved',
        dropdown: [
          {
            name: 'Ways to Give',
            path: '/donate',
            icon: <Heart size={16} />,
            description: 'Support our disaster relief efforts'
          },
          {
            name: 'Apply Partnership',
            path: '/partnership',
            icon: <Handshake size={16} />,
            description: 'Partner with us for greater impact'
          }
        ]
      },
      { name: t('navigation.contact'), path: '/contact' }
    ];

    // Add "Report" (create new report) only for authenticated users who are NOT regular users
    if (isAuthenticated && !isOnlyUser()) {
      baseItems.splice(2, 0, { name: 'Report', path: '/report/new' });
    }

    // Add "View Reports" for all authenticated users (including regular users)
    if (isAuthenticated) {
      baseItems.splice(isOnlyUser() ? 2 : 3, 0, { name: t('navigation.reports'), path: '/reports' });
    }

    // Add role-based navigation items for authenticated users
    if (isAuthenticated) {
      const authItems = [];

      // Add Dashboard for non-admin users
      if (!isOnlyUser() && !isAdmin()) {
        authItems.push({ name: t('navigation.dashboard'), path: '/dashboard' });
      }

      // Add Admin-only items
      if (isAdmin()) {
        authItems.push({ name: 'Admin Panel', path: '/admin' });
      }

      return [...baseItems, ...authItems];
    }

    return baseItems;
  };

  const isActivePage = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const handleLogout = () => {
    logout();
    setIsUserDropdownOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-200">
      {/* Main Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo Section */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="p-3 bg-gradient-to-br from-blue-600 via-blue-500 to-indigo-600 text-white rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <ShieldCheck size={24} className="drop-shadow-sm" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-gray-900 tracking-tight">{t('header.brand.name')}</h1>
              <p className="text-xs text-gray-600 font-medium -mt-1">{t('header.brand.subtitle')}</p>
            </div>
          </Link>

          {/* Center Navigation */}
          <nav className="hidden lg:flex items-center space-x-1" ref={dropdownRef}>
            {getNavItems().map((item) => (
              <div key={item.path} className="relative">
                {item.dropdown ? (
                  <div className="relative">
                    <button
                      onClick={() => setActiveDropdown(activeDropdown === item.name ? null : item.name)}
                      className={`flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                        isActivePage(item.path)
                          ? 'text-blue-600 bg-blue-50 border border-blue-200'
                          : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50 border border-transparent hover:border-gray-200'
                      }`}
                    >
                      <span>{item.name}</span>
                      <ChevronDown 
                        size={16} 
                        className={`transition-transform duration-300 ${
                          activeDropdown === item.name ? 'rotate-180' : ''
                        }`} 
                      />
                    </button>
                    
                    {/* Dropdown Menu */}
                    {activeDropdown === item.name && (
                      <div className="absolute top-full left-0 mt-2 w-72 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50 overflow-hidden">
                        {item.dropdown.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.path}
                            to={dropdownItem.path}
                            onClick={() => setActiveDropdown(null)}
                            className="flex items-start space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-all duration-300 group"
                          >
                            <div className="text-blue-600 mt-0.5 group-hover:scale-110 transition-transform duration-300">
                              {dropdownItem.icon}
                            </div>
                            <div>
                              <div className="font-semibold text-sm text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                                {dropdownItem.name}
                              </div>
                              {dropdownItem.description && (
                                <div className="text-xs text-gray-600 mt-1 leading-relaxed">
                                  {dropdownItem.description}
                                </div>
                              )}
                            </div>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      isActivePage(item.path)
                        ? 'text-blue-600 bg-blue-50 border border-blue-200'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50 border border-transparent hover:border-gray-200'
                    }`}
                  >
                    <span>{item.name}</span>
                    {isActivePage(item.path) && (
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-1 bg-blue-600 rounded-full"></div>
                    )}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="hidden lg:flex items-center space-x-3">
            {/* Language Switcher */}
            <div className="flex items-center">
              <LanguageSwitcher />
            </div>
            
            {/* Donation Button */}
            <Link
              to="/donate"
              className="group bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white px-6 py-2 rounded-lg text-sm font-semibold hover:from-red-600 hover:via-red-700 hover:to-red-800 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105 relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
              <Heart size={16} className="mr-2 relative z-10 group-hover:scale-110 transition-transform duration-300" />
              <span className="relative z-10">{t('navigation.donate')}</span>
            </Link>

            {isAuthenticated ? (
              /* User Profile Dropdown */
              <div className="relative" ref={userDropdownRef}>
                <button
                  onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                  className="flex items-center space-x-3 px-4 py-2 rounded-lg text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-all duration-300 border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md"
                >
                  <Avatar
                    src={user?.photoUrl}
                    alt={user?.name}
                    name={user?.name}
                    size="sm"
                  />
                  <div className="text-left hidden xl:block">
                    <div className="text-sm font-semibold text-gray-900">{user?.name}</div>
                    <div className="text-xs text-gray-500 font-medium">
                      {user?.roles?.filter(role => role).map(role => formatRoleName(role)).join(', ') || 'User'}
                    </div>
                  </div>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-300 text-gray-400 ${
                      isUserDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                {/* User Dropdown Menu */}
                {isUserDropdownOpen && (
                  <div className="absolute top-full right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-200 py-2 z-50 overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                      <div className="text-sm font-bold text-gray-900">{user?.name}</div>
                      <div className="text-xs text-gray-600 font-medium">{user?.email}</div>
                    </div>
                    
                    {!isOnlyUser() && !isAdmin() && (
                      <Link
                        to="/dashboard"
                        onClick={() => setIsUserDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <User size={16} className="mr-3 text-gray-400" />
                        Dashboard
                      </Link>
                    )}
                    
                    <hr className="my-1 border-gray-200" />
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <LogOut size={16} className="mr-3 text-gray-400" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              /* Login Button */
              <Link
                to="/login"
                className="flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300 font-medium text-sm border border-gray-200 hover:border-blue-300 shadow-sm hover:shadow-md"
              >
                <User size={16} />
                <span>Login</span>
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-300 border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200 shadow-xl">
          <div className="px-6 py-6 space-y-3">
            {getNavItems().map((item) => (
              <div key={item.path}>
                {item.dropdown ? (
                  <div className="space-y-2">
                    <div className="text-gray-900 font-semibold text-base px-4 py-2">
                      {item.name}
                    </div>
                    {item.dropdown.map((dropdownItem) => (
                      <Link
                        key={dropdownItem.path}
                        to={dropdownItem.path}
                        className="flex items-center space-x-3 px-6 py-3 rounded-lg text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300 border border-transparent hover:border-blue-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <div className="text-blue-600">
                          {dropdownItem.icon}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{dropdownItem.name}</div>
                          {dropdownItem.description && (
                            <div className="text-xs text-gray-500 mt-1">{dropdownItem.description}</div>
                          )}
                        </div>
                      </Link>
                    ))}
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 ${
                      isActivePage(item.path)
                        ? 'text-blue-600 bg-blue-50 border border-blue-200'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50 border border-transparent hover:border-gray-200'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}

            {/* Mobile Language Switcher */}
            <div className="pt-6 border-t border-gray-200 pb-6">
              <LanguageSwitcher />
            </div>

            {/* Mobile Auth Section */}
            <div className="pt-6 border-t border-gray-200">
              {isAuthenticated ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <Avatar
                      src={user?.photoUrl}
                      alt={user?.name}
                      name={user?.name}
                      size="lg"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{user?.name}</div>
                      <div className="text-sm text-gray-500">{user?.email}</div>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      handleLogout();
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center space-x-3 text-gray-600 hover:text-red-600 transition-colors py-2 w-full text-left"
                  >
                    <LogOut size={20} />
                    <span className="text-lg font-medium">Logout</span>
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <Link
                    to="/login"
                    className="flex items-center justify-center space-x-3 w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <User size={20} />
                    <span>Login</span>
                  </Link>
                  <Link
                    to="/donate"
                    className="flex items-center justify-center space-x-3 w-full bg-gradient-to-r from-red-600 via-red-600 to-red-700 text-white px-8 py-4 rounded-2xl hover:from-red-700 hover:via-red-700 hover:to-red-800 transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105 relative overflow-hidden"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Heart size={20} className="animate-pulse" />
                    <span>{t('header.topBar.donateNow')}</span>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
