# Internationalization (i18n) Implementation

## Overview

The DisasterWatch application now supports internationalization with English and Myanmar (Burmese) languages. This implementation uses `react-i18next` for translation management and provides a seamless user experience across different languages.

## Features

### ✅ Implemented Features

1. **Language Support**
   - English (default)
   - Myanmar (မြန်မာ)
   - Bidirectional translation support

2. **Language Switcher**
   - Accessible dropdown component in header
   - Available on both desktop and mobile
   - Persistent language preference (localStorage)
   - Smooth language switching without page reload

3. **Font Support**
   - Proper Myanmar Unicode font rendering
   - Google Fonts integration for Myanmar text
   - Optimized text rendering for both languages

4. **Translated Content**
   - Navigation menu items
   - Home page hero section
   - Common UI elements (buttons, forms, etc.)
   - Error messages and status text

5. **Technical Features**
   - Fallback to English if translation missing
   - Responsive design maintained across languages
   - Accessibility standards compliance
   - SEO-friendly language attributes

## File Structure

```
src/
├── i18n/
│   ├── index.ts                 # i18n configuration
│   └── locales/
│       ├── en.json             # English translations
│       └── my.json             # Myanmar translations
├── components/
│   └── LanguageSwitcher/
│       └── LanguageSwitcher.tsx # Language switcher component
├── styles/
│   └── i18n.css                # Internationalization styles
└── pages/                      # Updated pages with i18n support
```

## Usage

### Adding Translations

1. **Add to English file** (`src/i18n/locales/en.json`):
```json
{
  "section": {
    "key": "English text"
  }
}
```

2. **Add to Myanmar file** (`src/i18n/locales/my.json`):
```json
{
  "section": {
    "key": "မြန်မာစာ"
  }
}
```

### Using in Components

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('section.key')}</h1>
    </div>
  );
};
```

### Language Switching

The language switcher is automatically included in the header and provides:
- Visual language selection with flags
- Native language names
- Persistent preference storage

## Translation Keys Structure

### Navigation
- `navigation.home` - Home
- `navigation.reports` - Reports
- `navigation.about` - About
- `navigation.contact` - Contact
- etc.

### Common Elements
- `common.loading` - Loading states
- `common.error` - Error messages
- `common.submit` - Form submissions
- etc.

### Page-Specific
- `home.hero.title` - Home page hero title
- `home.hero.description` - Home page description
- `reports.title` - Reports page title
- etc.

## Myanmar Language Support

### Font Rendering
- Uses Google Fonts 'Noto Sans Myanmar'
- Fallback fonts: 'Myanmar Text', 'Padauk'
- Optimized text rendering with proper line spacing

### Text Adjustments
- Increased line height for better readability
- Proper font weights for headings and body text
- Optimized letter spacing for buttons and navigation

## Browser Support

- Modern browsers with ES6+ support
- Proper Unicode rendering for Myanmar script
- Responsive design across all devices
- Accessibility features maintained

## Performance Considerations

- Lazy loading of translation files
- Efficient re-rendering on language change
- Minimal bundle size impact
- Cached language preferences

## Future Enhancements

### Planned Features
1. **Additional Languages**
   - Thai
   - Vietnamese
   - Indonesian

2. **Advanced Features**
   - Pluralization rules
   - Date/time localization
   - Number formatting
   - Currency localization

3. **Content Management**
   - Translation management interface
   - Crowdsourced translations
   - Professional translation integration

## Development Guidelines

### Adding New Translations

1. Always add keys to both language files
2. Use descriptive, hierarchical key names
3. Test with both languages before committing
4. Ensure proper font rendering for Myanmar text

### Best Practices

1. **Key Naming**
   ```
   section.subsection.element
   page.component.action
   ```

2. **Component Updates**
   ```tsx
   // Always import useTranslation
   import { useTranslation } from 'react-i18next';
   
   // Use descriptive translation keys
   const { t } = useTranslation();
   return <button>{t('common.submit')}</button>;
   ```

3. **Testing**
   - Test all pages in both languages
   - Verify text doesn't break layouts
   - Check mobile responsiveness
   - Validate accessibility features

## Troubleshooting

### Common Issues

1. **Missing Translations**
   - Check console for missing key warnings
   - Verify key exists in both language files
   - Ensure proper key hierarchy

2. **Font Rendering Issues**
   - Verify Google Fonts are loading
   - Check browser Unicode support
   - Clear browser cache if needed

3. **Layout Breaking**
   - Test with longer Myanmar text
   - Adjust CSS for text overflow
   - Use responsive design principles

### Debug Mode

Enable debug mode in development:
```typescript
// In src/i18n/index.ts
debug: process.env.NODE_ENV === 'development'
```

This will log missing translations and other i18n issues to the console.
