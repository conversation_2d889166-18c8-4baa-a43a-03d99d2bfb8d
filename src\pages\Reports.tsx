import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import {
  Search,
  MapPin,
  Calendar,
  User,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  X,
  SlidersHorizontal,
  ImageIcon,
  CheckCircle,
  Clock,
  AlertTriangle,
  Grid3X3,
  List,
  Shield,
  Activity,
  Flame,
  Waves,
  Mountain,
  Wind,
  Droplets,
  Building,
  Truck,
  RefreshCw,
  BarChart3,
  Sparkles,
  Heart
} from 'lucide-react';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';

// Hooks
import { useAuth } from '../hooks/useAuth';
import { useRoles } from '../hooks/useRoles';

// Data
import { mockReports } from '../data/mockData';
import { Report } from '../types';

const Reports: React.FC = () => {
  // Auth and roles
  const { isAuthenticated } = useAuth();
  const { isAdmin, isCj, isOnlyUser } = useRoles();

  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDisasterType, setSelectedDisasterType] = useState<string>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showOnlyWithImages, setShowOnlyWithImages] = useState(false);
  const [sortBy, setSortBy] = useState<string>('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(false);

  // Role-based permissions
  const canViewSensitiveInfo = isAdmin || isCj;
  const isRegularUser = isOnlyUser;

  // Image loading states
  const [imageLoadingStates, setImageLoadingStates] = useState<{[key: string]: boolean}>({});
  const [imageErrorStates, setImageErrorStates] = useState<{[key: string]: boolean}>({});

  // Filter options
  const disasterTypes = ['all', 'flood', 'fire', 'earthquake', 'storm', 'landslide', 'accident', 'other'];
  const severityLevels = ['all', 'low', 'medium', 'high', 'critical'];
  // Status options based on user role
  const statusOptions = isRegularUser
    ? ['all', 'verified'] // Regular users only see verified reports
    : ['all', 'pending', 'verified', 'resolved']; // Admin/CJ see all statuses
  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'severity', label: 'Severity' },
    { value: 'location', label: 'Location' }
  ];

  // Filter and sort reports based on user role
  const filteredAndSortedReports = useMemo(() => {
    let filtered = mockReports.filter(report => {
      // Role-based filtering: Regular users only see verified reports
      if (isRegularUser && report.status !== 'verified') {
        return false;
      }

      // Search filter
      if (searchTerm && !report.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !report.description.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !report.location.address.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Disaster type filter
      if (selectedDisasterType !== 'all' && report.disasterType !== selectedDisasterType) {
        return false;
      }

      // Severity filter
      if (selectedSeverity !== 'all' && report.severity !== selectedSeverity) {
        return false;
      }

      // Status filter
      if (selectedStatus !== 'all' && report.status !== selectedStatus) {
        return false;
      }

      // Images filter
      if (showOnlyWithImages && (!report.photos || report.photos.length === 0)) {
        return false;
      }

      return true;
    });

    // Sort reports
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'severity':
          const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
          return (severityOrder[b.severity as keyof typeof severityOrder] || 0) -
                 (severityOrder[a.severity as keyof typeof severityOrder] || 0);
        case 'location':
          return a.location.address.localeCompare(b.location.address);
        default:
          return 0;
      }
    });

    return filtered;
  }, [mockReports, searchTerm, selectedDisasterType, selectedSeverity, selectedStatus, showOnlyWithImages, sortBy, isRegularUser]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedReports.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedReports = filteredAndSortedReports.slice(startIndex, startIndex + itemsPerPage);

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedDisasterType, selectedSeverity, selectedStatus, showOnlyWithImages, sortBy]);

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedDisasterType('all');
    setSelectedSeverity('all');
    setSelectedStatus('all');
    setShowOnlyWithImages(false);
    setSortBy('newest');
    setCurrentPage(1);
  };

  // Get disaster type icon component
  const getDisasterIcon = (type: string) => {
    const iconProps = { size: 16, className: "text-white" };
    switch (type) {
      case 'flood': return <Waves {...iconProps} />;
      case 'fire': return <Flame {...iconProps} />;
      case 'earthquake': return <Mountain {...iconProps} />;
      case 'storm': return <Wind {...iconProps} />;
      case 'landslide': return <Mountain {...iconProps} />;
      case 'accident': return <Truck {...iconProps} />;
      default: return <AlertTriangle {...iconProps} />;
    }
  };

  // Get disaster type color
  const getDisasterColor = (type: string) => {
    switch (type) {
      case 'flood': return 'from-blue-500 to-blue-600';
      case 'fire': return 'from-red-500 to-orange-500';
      case 'earthquake': return 'from-yellow-600 to-orange-600';
      case 'storm': return 'from-gray-500 to-gray-600';
      case 'landslide': return 'from-amber-600 to-yellow-600';
      case 'accident': return 'from-purple-500 to-purple-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  // Get disaster type display name
  const getDisasterTypeName = (type: string) => {
    switch (type) {
      case 'flood': return 'Flood';
      case 'fire': return 'Fire';
      case 'earthquake': return 'Earthquake';
      case 'storm': return 'Storm';
      case 'landslide': return 'Landslide';
      case 'accident': return 'Accident';
      default: return 'Other';
    }
  };

  // Get severity color classes
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Enhanced default image handling with optimized quality and aspect ratios
  const getDefaultImage = (type: string) => {
    const defaultImages = {
      flood: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      fire: 'https://images.unsplash.com/photo-1574169208507-84376144848b?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      earthquake: 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      storm: 'https://images.unsplash.com/photo-1527482797697-8795b05a13fe?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      landslide: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      accident: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      other: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&crop=center&auto=format&q=80'
    };
    return defaultImages[type as keyof typeof defaultImages] || defaultImages.other;
  };

  const handleImageLoad = (reportId: string) => {
    setImageLoadingStates(prev => ({ ...prev, [reportId]: false }));
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>, disasterType: string, reportId: string) => {
    const target = e.target as HTMLImageElement;
    target.src = getDefaultImage(disasterType);
    setImageErrorStates(prev => ({ ...prev, [reportId]: true }));
    setImageLoadingStates(prev => ({ ...prev, [reportId]: false }));
  };

  const handleImageLoadStart = (reportId: string) => {
    setImageLoadingStates(prev => ({ ...prev, [reportId]: true }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-white to-indigo-50/20">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        {/* Professional Page Header */}
        <div className="text-center mb-16">
          {/* Status Badges */}
          <div className="flex flex-wrap items-center justify-center gap-3 mb-8">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-50 border border-blue-200 text-blue-700 text-sm font-semibold hover:bg-blue-100 transition-colors duration-200">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
              <Activity size={16} className="mr-2" />
              Live Reports
            </div>
            <button
              onClick={() => {
                setIsLoading(true);
                setTimeout(() => {
                  window.location.reload();
                }, 500);
              }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-gray-200 text-gray-700 text-sm font-semibold hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
              title="Refresh Reports"
              disabled={isLoading}
            >
              <RefreshCw size={16} className={`mr-2 ${isLoading ? 'animate-spin' : 'group-hover:rotate-180'} transition-transform duration-300`} />
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-indigo-50 border border-indigo-200 text-indigo-700 text-sm font-semibold">
              <Sparkles size={16} className="mr-2" />
              Enhanced UI
            </div>
          </div>

          {/* Main Title */}
          <div className="relative mb-8">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              {isRegularUser ? 'Community' : 'Emergency'}
              <br />
              <span className="text-blue-600">
                {isRegularUser ? 'Safety Updates' : 'Reports Hub'}
              </span>
            </h1>
          </div>

          {/* Description */}
          <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12">
            {isRegularUser
              ? 'Stay informed about verified disaster reports and safety updates in your community. Access reliable information to keep yourself and your family safe.'
              : 'Real-time disaster reports from communities worldwide. Browse verified incidents, track emergency responses, and stay informed about ongoing situations in your area.'
            }
          </p>

          {/* Statistics Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-5xl mx-auto">
            <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <BarChart3 className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {filteredAndSortedReports.length}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">reports</div>
                </div>
              </div>
              <div className="text-gray-900 font-semibold mb-1">Total Reports</div>
              <div className="text-sm text-gray-500">Currently displayed</div>
            </div>

            <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {filteredAndSortedReports.filter(r => r.status === 'verified').length}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">verified</div>
                </div>
              </div>
              <div className="text-gray-900 font-semibold mb-1">Verified</div>
              <div className="text-sm text-gray-500">
                {filteredAndSortedReports.length > 0 ? Math.round((filteredAndSortedReports.filter(r => r.status === 'verified').length / filteredAndSortedReports.length) * 100) : 0}% of total
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-orange-100 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {filteredAndSortedReports.filter(r => r.severity === 'critical' || r.severity === 'high').length}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">urgent</div>
                </div>
              </div>
              <div className="text-gray-900 font-semibold mb-1">High Priority</div>
              <div className="text-sm text-gray-500">Critical & High severity</div>
            </div>

            <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Heart className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {filteredAndSortedReports.filter(r => r.assistanceLog && r.assistanceLog.length > 0).length}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">active</div>
                </div>
              </div>
              <div className="text-gray-900 font-semibold mb-1">Active Response</div>
              <div className="text-sm text-gray-500">Reports with assistance</div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Reports;